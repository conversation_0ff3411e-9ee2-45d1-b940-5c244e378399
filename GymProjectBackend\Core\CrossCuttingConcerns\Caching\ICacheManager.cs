using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache Manager Interface - CompanyContext entegrasyonu ile
    /// Multi-tenant cache yönetimi için
    /// </summary>
    public interface ICacheManager
    {
        #region Synchronous Methods

        /// <summary>
        /// Cache'den veri al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        T Get<T>(string entityName, object entityId);

        /// <summary>
        /// Cache'e veri kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <param name="value">Kaydedilecek veri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        void Set<T>(string entityName, object entityId, T value, TimeSpan? expiry = null);

        /// <summary>
        /// Cache'den veri sil - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        bool Remove(string entityName, object entityId);

        /// <summary>
        /// Entity tipine göre tüm cache'leri temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        long RemoveByEntityType(string entityName);

        /// <summary>
        /// Mevcut company'nin tüm cache'lerini temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <returns>Silinen key sayısı</returns>
        long RemoveAllCompanyCache();

        /// <summary>
        /// Liste cache'i al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Cache'deki liste veya default(T)</returns>
        T GetList<T>(string entityName, int page = 1, int pageSize = 10);

        /// <summary>
        /// Liste cache'i kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="value">Kaydedilecek liste</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        void SetList<T>(string entityName, T value, int page = 1, int pageSize = 10, TimeSpan? expiry = null);

        /// <summary>
        /// Arama cache'i al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="page">Sayfa numarası</param>
        /// <returns>Cache'deki arama sonucu veya default(T)</returns>
        T GetSearch<T>(string entityName, string searchTerm, int page = 1);

        /// <summary>
        /// Arama cache'i kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="value">Kaydedilecek arama sonucu</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        void SetSearch<T>(string entityName, string searchTerm, T value, int page = 1, TimeSpan? expiry = null);

        /// <summary>
        /// İstatistik cache'i al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="statType">İstatistik tipi (daily, monthly, yearly)</param>
        /// <param name="period">Dönem (2024-01, 2024 vb.)</param>
        /// <returns>Cache'deki istatistik veya default(T)</returns>
        T GetStats<T>(string statType, string period);

        /// <summary>
        /// İstatistik cache'i kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="statType">İstatistik tipi (daily, monthly, yearly)</param>
        /// <param name="period">Dönem (2024-01, 2024 vb.)</param>
        /// <param name="value">Kaydedilecek istatistik</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        void SetStats<T>(string statType, string period, T value, TimeSpan? expiry = null);

        #endregion

        #region Asynchronous Methods

        /// <summary>
        /// Cache'den veri al - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        Task<T> GetAsync<T>(string entityName, object entityId);

        /// <summary>
        /// Cache'e veri kaydet - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <param name="value">Kaydedilecek veri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        Task SetAsync<T>(string entityName, object entityId, T value, TimeSpan? expiry = null);

        /// <summary>
        /// Cache'den veri sil - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        Task<bool> RemoveAsync(string entityName, object entityId);

        /// <summary>
        /// Entity tipine göre tüm cache'leri temizle - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> RemoveByEntityTypeAsync(string entityName);

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Birden fazla entity'yi aynı anda cache'den al
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityIds">Entity ID listesi</param>
        /// <returns>ID-Value dictionary</returns>
        Dictionary<object, T> GetMultiple<T>(string entityName, IEnumerable<object> entityIds);

        /// <summary>
        /// Birden fazla entity'yi aynı anda cache'e kaydet
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityValuePairs">ID-Value çiftleri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        void SetMultiple<T>(string entityName, Dictionary<object, T> entityValuePairs, TimeSpan? expiry = null);

        #endregion

        #region Pattern Operations

        /// <summary>
        /// Liste cache'lerini temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        long RemoveListCache(string entityName);

        /// <summary>
        /// Arama cache'lerini temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        long RemoveSearchCache(string entityName);

        #endregion
    }
}
