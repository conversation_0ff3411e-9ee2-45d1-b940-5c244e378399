using Microsoft.AspNetCore.Mvc;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Results;
using System;
using System.Collections.Generic;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Redis Cache Test Controller
    /// Development ortamında cache test işlemleri için
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CacheTestController : ControllerBase
    {
        private readonly ICacheService _cacheService;

        public CacheTestController(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        /// <summary>
        /// Redis bağlantı testi - Ping/Pong
        /// </summary>
        [HttpGet("ping")]
        public IActionResult Ping()
        {
            try
            {
                var pingTime = _cacheService.Ping();
                
                if (pingTime >= 0)
                {
                    return Ok(new SuccessDataResult<object>(new 
                    { 
                        message = "Redis connection successful", 
                        pingTime = $"{pingTime}ms",
                        timestamp = DateTime.UtcNow
                    }));
                }
                else
                {
                    return BadRequest(new ErrorResult("Redis connection failed"));
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Redis connection error: {ex.Message}"));
            }
        }

        /// <summary>
        /// Redis health check
        /// </summary>
        [HttpGet("health")]
        public IActionResult Health()
        {
            try
            {
                var isHealthy = _cacheService.IsHealthy();
                var statistics = _cacheService.GetStatistics();
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    isHealthy,
                    statistics,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Health check failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Temel cache set/get testi
        /// </summary>
        [HttpPost("test-basic")]
        public IActionResult TestBasic([FromBody] TestCacheRequest request)
        {
            try
            {
                // Cache'e veri kaydet
                _cacheService.Set(request.Key, request.Value, TimeSpan.FromMinutes(5));
                
                // Cache'den veri oku
                var cachedValue = _cacheService.Get<string>(request.Key);
                
                // Key'in var olup olmadığını kontrol et
                var exists = _cacheService.Exists(request.Key);
                
                // TTL kontrol et
                var ttl = _cacheService.GetTimeToLive(request.Key);
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    message = "Cache test successful",
                    originalValue = request.Value,
                    cachedValue,
                    exists,
                    ttl = ttl?.TotalSeconds,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Generic type cache testi
        /// </summary>
        [HttpPost("test-generic")]
        public IActionResult TestGeneric()
        {
            try
            {
                var testObject = new TestObject
                {
                    Id = 123,
                    Name = "Test User",
                    Email = "<EMAIL>",
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                var key = "test:object:123";
                
                // Object cache'e kaydet
                _cacheService.Set(key, testObject, TimeSpan.FromMinutes(10));
                
                // Object cache'den oku
                var cachedObject = _cacheService.Get<TestObject>(key);
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    message = "Generic cache test successful",
                    originalObject = testObject,
                    cachedObject,
                    isEqual = testObject.Id == cachedObject?.Id && testObject.Name == cachedObject?.Name,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Generic cache test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Pattern-based operations testi
        /// </summary>
        [HttpPost("test-pattern")]
        public IActionResult TestPattern()
        {
            try
            {
                var testKeys = new List<string>
                {
                    "gym:1:member:101",
                    "gym:1:member:102", 
                    "gym:1:payment:201",
                    "gym:2:member:301"
                };

                // Test verileri kaydet
                foreach (var key in testKeys)
                {
                    _cacheService.Set(key, $"Value for {key}", TimeSpan.FromMinutes(5));
                }

                // Pattern ile key'leri getir
                var gym1Keys = _cacheService.GetKeys("gym:1:*");
                var memberKeys = _cacheService.GetKeys("gym:*:member:*");
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    message = "Pattern test successful",
                    testKeys,
                    gym1Keys,
                    memberKeys,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Pattern test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache temizleme testi
        /// </summary>
        [HttpDelete("test-cleanup")]
        public IActionResult TestCleanup()
        {
            try
            {
                // Test pattern'ine göre cache'leri temizle
                var deletedCount = _cacheService.RemoveByPattern("test:*");
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    message = "Cache cleanup successful",
                    deletedCount,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache cleanup failed: {ex.Message}"));
            }
        }
    }

    public class TestCacheRequest
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class TestObject
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; }
    }
}
