using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.Logging;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache Manager - CompanyContext entegrasyonu ile
    /// Multi-tenant cache yönetimi için
    /// </summary>
    public class CacheManager : ICacheManager
    {
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly ILogger<CacheManager> _logger;

        public CacheManager(
            ICacheService cacheService,
            ICompanyContext companyContext,
            ILogger<CacheManager> logger)
        {
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
            _companyContext = companyContext ?? throw new ArgumentNullException(nameof(companyContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Synchronous Methods

        /// <summary>
        /// Cache'den veri al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        public T Get<T>(string entityName, object entityId)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return default(T);
                }

                var key = CacheKeyHelper.GenerateKey(companyId, entityName, entityId);
                return _cacheService.Get<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache value for entity: {EntityName}, ID: {EntityId}", entityName, entityId);
                return default(T);
            }
        }

        /// <summary>
        /// Cache'e veri kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <param name="value">Kaydedilecek veri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        public void Set<T>(string entityName, object entityId, T value, TimeSpan? expiry = null)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return;
                }

                var key = CacheKeyHelper.GenerateKey(companyId, entityName, entityId);
                _cacheService.Set(key, value, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache value for entity: {EntityName}, ID: {EntityId}", entityName, entityId);
            }
        }

        /// <summary>
        /// Cache'den veri sil - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        public bool Remove(string entityName, object entityId)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return false;
                }

                var key = CacheKeyHelper.GenerateKey(companyId, entityName, entityId);
                return _cacheService.Remove(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache value for entity: {EntityName}, ID: {EntityId}", entityName, entityId);
                return false;
            }
        }

        /// <summary>
        /// Entity tipine göre tüm cache'leri temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        public long RemoveByEntityType(string entityName)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return 0;
                }

                var pattern = CacheKeyHelper.GenerateEntityPattern(companyId, entityName);
                return _cacheService.RemoveByPattern(pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache by entity type: {EntityName}", entityName);
                return 0;
            }
        }

        /// <summary>
        /// Mevcut company'nin tüm cache'lerini temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <returns>Silinen key sayısı</returns>
        public long RemoveAllCompanyCache()
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return 0;
                }

                var pattern = CacheKeyHelper.GenerateCompanyPattern(companyId);
                return _cacheService.RemoveByPattern(pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing all company cache");
                return 0;
            }
        }

        /// <summary>
        /// Liste cache'i al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Cache'deki liste veya default(T)</returns>
        public T GetList<T>(string entityName, int page = 1, int pageSize = 10)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return default(T);
                }

                var key = CacheKeyHelper.GenerateListKey(companyId, entityName, page, pageSize);
                return _cacheService.Get<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting list cache for entity: {EntityName}, Page: {Page}, PageSize: {PageSize}", 
                                entityName, page, pageSize);
                return default(T);
            }
        }

        /// <summary>
        /// Liste cache'i kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="value">Kaydedilecek liste</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        public void SetList<T>(string entityName, T value, int page = 1, int pageSize = 10, TimeSpan? expiry = null)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return;
                }

                var key = CacheKeyHelper.GenerateListKey(companyId, entityName, page, pageSize);
                _cacheService.Set(key, value, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting list cache for entity: {EntityName}, Page: {Page}, PageSize: {PageSize}", 
                                entityName, page, pageSize);
            }
        }

        /// <summary>
        /// Arama cache'i al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="page">Sayfa numarası</param>
        /// <returns>Cache'deki arama sonucu veya default(T)</returns>
        public T GetSearch<T>(string entityName, string searchTerm, int page = 1)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return default(T);
                }

                var key = CacheKeyHelper.GenerateSearchKey(companyId, entityName, searchTerm, page);
                return _cacheService.Get<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search cache for entity: {EntityName}, SearchTerm: {SearchTerm}, Page: {Page}",
                                entityName, searchTerm, page);
                return default(T);
            }
        }

        /// <summary>
        /// Arama cache'i kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="value">Kaydedilecek arama sonucu</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        public void SetSearch<T>(string entityName, string searchTerm, T value, int page = 1, TimeSpan? expiry = null)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return;
                }

                var key = CacheKeyHelper.GenerateSearchKey(companyId, entityName, searchTerm, page);
                _cacheService.Set(key, value, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting search cache for entity: {EntityName}, SearchTerm: {SearchTerm}, Page: {Page}",
                                entityName, searchTerm, page);
            }
        }

        /// <summary>
        /// İstatistik cache'i al - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="statType">İstatistik tipi (daily, monthly, yearly)</param>
        /// <param name="period">Dönem (2024-01, 2024 vb.)</param>
        /// <returns>Cache'deki istatistik veya default(T)</returns>
        public T GetStats<T>(string statType, string period)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return default(T);
                }

                var key = CacheKeyHelper.GenerateStatsKey(companyId, statType, period);
                return _cacheService.Get<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stats cache for StatType: {StatType}, Period: {Period}",
                                statType, period);
                return default(T);
            }
        }

        /// <summary>
        /// İstatistik cache'i kaydet - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="statType">İstatistik tipi (daily, monthly, yearly)</param>
        /// <param name="period">Dönem (2024-01, 2024 vb.)</param>
        /// <param name="value">Kaydedilecek istatistik</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        public void SetStats<T>(string statType, string period, T value, TimeSpan? expiry = null)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return;
                }

                var key = CacheKeyHelper.GenerateStatsKey(companyId, statType, period);
                _cacheService.Set(key, value, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting stats cache for StatType: {StatType}, Period: {Period}",
                                statType, period);
            }
        }

        #endregion

        #region Asynchronous Methods

        /// <summary>
        /// Cache'den veri al - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        public async Task<T> GetAsync<T>(string entityName, object entityId)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return default(T);
                }

                var key = CacheKeyHelper.GenerateKey(companyId, entityName, entityId);
                return await _cacheService.GetAsync<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache value async for entity: {EntityName}, ID: {EntityId}", entityName, entityId);
                return default(T);
            }
        }

        /// <summary>
        /// Cache'e veri kaydet - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <param name="value">Kaydedilecek veri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        public async Task SetAsync<T>(string entityName, object entityId, T value, TimeSpan? expiry = null)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return;
                }

                var key = CacheKeyHelper.GenerateKey(companyId, entityName, entityId);
                await _cacheService.SetAsync(key, value, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache value async for entity: {EntityName}, ID: {EntityId}", entityName, entityId);
            }
        }

        /// <summary>
        /// Cache'den veri sil - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        public async Task<bool> RemoveAsync(string entityName, object entityId)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return false;
                }

                var key = CacheKeyHelper.GenerateKey(companyId, entityName, entityId);
                return await _cacheService.RemoveAsync(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache value async for entity: {EntityName}, ID: {EntityId}", entityName, entityId);
                return false;
            }
        }

        /// <summary>
        /// Entity tipine göre tüm cache'leri temizle - Async - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        public async Task<long> RemoveByEntityTypeAsync(string entityName)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return 0;
                }

                var pattern = CacheKeyHelper.GenerateEntityPattern(companyId, entityName);
                return await _cacheService.RemoveByPatternAsync(pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache by entity type async: {EntityName}", entityName);
                return 0;
            }
        }

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Birden fazla entity'yi aynı anda cache'den al
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityIds">Entity ID listesi</param>
        /// <returns>ID-Value dictionary</returns>
        public Dictionary<object, T> GetMultiple<T>(string entityName, IEnumerable<object> entityIds)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return new Dictionary<object, T>();
                }

                var keys = entityIds.Select(id => CacheKeyHelper.GenerateKey(companyId, entityName, id)).ToList();
                var cacheResults = _cacheService.GetMultiple<T>(keys);

                var result = new Dictionary<object, T>();
                var keyArray = keys.ToArray();
                var idArray = entityIds.ToArray();

                for (int i = 0; i < keyArray.Length; i++)
                {
                    if (cacheResults.ContainsKey(keyArray[i]))
                    {
                        result[idArray[i]] = cacheResults[keyArray[i]];
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting multiple cache values for entity: {EntityName}", entityName);
                return new Dictionary<object, T>();
            }
        }

        /// <summary>
        /// Birden fazla entity'yi aynı anda cache'e kaydet
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <param name="entityValuePairs">ID-Value çiftleri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        public void SetMultiple<T>(string entityName, Dictionary<object, T> entityValuePairs, TimeSpan? expiry = null)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return;
                }

                var keyValuePairs = new Dictionary<string, T>();
                foreach (var kvp in entityValuePairs)
                {
                    var key = CacheKeyHelper.GenerateKey(companyId, entityName, kvp.Key);
                    keyValuePairs[key] = kvp.Value;
                }

                _cacheService.SetMultiple(keyValuePairs, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting multiple cache values for entity: {EntityName}", entityName);
            }
        }

        #endregion

        #region Pattern Operations

        /// <summary>
        /// Liste cache'lerini temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        public long RemoveListCache(string entityName)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return 0;
                }

                var pattern = CacheKeyHelper.GenerateListPattern(companyId, entityName);
                return _cacheService.RemoveByPattern(pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing list cache for entity: {EntityName}", entityName);
                return 0;
            }
        }

        /// <summary>
        /// Arama cache'lerini temizle - CompanyContext entegrasyonu ile
        /// </summary>
        /// <param name="entityName">Entity adı (CacheKeyHelper.EntityNames'den)</param>
        /// <returns>Silinen key sayısı</returns>
        public long RemoveSearchCache(string entityName)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID: {CompanyId}", companyId);
                    return 0;
                }

                var pattern = CacheKeyHelper.GenerateSearchPattern(companyId, entityName);
                return _cacheService.RemoveByPattern(pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing search cache for entity: {EntityName}", entityName);
                return 0;
            }
        }

        #endregion
    }
}
